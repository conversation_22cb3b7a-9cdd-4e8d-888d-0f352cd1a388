using Pulumi;
using Network = Pulumi.AzureNative.Network;

namespace Sleekflow.Infras.Components.VNet;

public class WebhookBridgeVNetOutput
{
    public Network.VirtualNetwork VNet { get; set; }

    public Network.Subnet Subnet { get; set; }

    public Network.PublicIPAddress PublicIp { get; set; }

    public Network.NatGateway NatGateway { get; set; }

    public Output<string> StaticOutboundIpAddress { get; set; }

    public WebhookBridgeVNetOutput(
        Network.VirtualNetwork vNet,
        Network.Subnet subnet,
        Network.PublicIPAddress publicIp,
        Network.NatGateway natGateway,
        Output<string> staticOutboundIpAddress)
    {
        VNet = vNet;
        Subnet = subnet;
        PublicIp = publicIp;
        NatGateway = natGateway;
        StaticOutboundIpAddress = staticOutboundIpAddress;
    }
}