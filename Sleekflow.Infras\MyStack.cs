using Pulumi;
using Pulumi.AzureNative.AppPlatform.V20230501Preview;
using Pulumi.AzureNative.DocumentDB.Inputs;
using Pulumi.AzureNative.Insights.Inputs;
using Pulumi.AzureNative.Resources;
using Pulumi.AzureNative.Storage;
using Pulumi.AzureNative.Web;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Infras.Components;
using Sleekflow.Infras.Components.AuditHub;
using Sleekflow.Infras.Components.Auth0s;
using Sleekflow.Infras.Components.CommerceHub;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.CrmHub;
using Sleekflow.Infras.Components.Dashboards;
using Sleekflow.Infras.Components.DbScalar;
using Sleekflow.Infras.Components.EmailHub;
using Sleekflow.Infras.Components.FlowHub;
using Sleekflow.Infras.Components.IntelligentHub;
using Sleekflow.Infras.Components.InternalIntegrationHub;
using Sleekflow.Infras.Components.MessagingHub;
using Sleekflow.Infras.Components.Opa;
using Sleekflow.Infras.Components.PublicApiGateway;
using Sleekflow.Infras.Components.ShareHub;
using Sleekflow.Infras.Components.SupportHub;
using Sleekflow.Infras.Components.TenantHub;
using Sleekflow.Infras.Components.TicketingHub;
using Sleekflow.Infras.Components.UserEventHub;
using Sleekflow.Infras.Components.VNet;
using Sleekflow.Infras.Components.WebhookHub;
using Sleekflow.Infras.Components.WebhookBridge;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using AppConfiguration = Pulumi.AzureNative.AppConfiguration;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Deployment = Pulumi.Deployment;
using DocumentDB = Pulumi.AzureNative.DocumentDB;
using Insights = Pulumi.AzureNative.Insights;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using SignalRService = Pulumi.AzureNative.SignalRService;
using Storage = Pulumi.AzureNative.Storage;
using Random = Pulumi.Random;

namespace Sleekflow.Infras;

internal class MyStack : Stack
{
    private static string _flowHubDatabaseId = string.Empty;
    private static string _auditHubDbId = string.Empty;
    private static string _commerceHubDbId = string.Empty;
    private static string _crmHubDbId = string.Empty;
    private static string _intelligentHubDbId = string.Empty;
    private static string _messageHubDbId = string.Empty;
    private static string _shareHubDbId = string.Empty;
    private static string _supportHubDbId = string.Empty;
    private static string _ticketingHubDbId = string.Empty;
    private static string _userEventHubDbId = string.Empty;
    private static string _internalIntegrationHubDbId = string.Empty;

    public MyStack()
    {
        var myConfig = new MyConfig();
        var auth0Config = new Auth0Config();
        var gcpConfig = new GcpConfig();
        var coreSqlDbConfig = new CoreSqlDbConfig();

        // Deploy to staging and dev only
        new Auth0s()
            .InitAuth0(auth0Config);

        var stackReference = new StackReference(
            $"{Deployment.Instance.OrganizationName}/{Deployment.Instance.ProjectName}/{Deployment.Instance.StackName}");

        var resourceGroup = new ResourceGroup("sleekflow-resource-group-" + myConfig.Name);

        var (registry, adminUsername, adminPassword) = InitContainerRegistry(resourceGroup);

        var configurationStore = new AppConfiguration.ConfigurationStore(
            "sleekflow-app-configuration",
            new AppConfiguration.ConfigurationStoreArgs
            {
                ResourceGroupName = resourceGroup.Name,
                Sku = new AppConfiguration.Inputs.SkuArgs
                {
                    Name = "Standard",
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var logAnalyticsWorkspace = new OperationalInsights.Workspace(
            "sleekflow",
            new OperationalInsights.WorkspaceArgs
            {
                ResourceGroupName = resourceGroup.Name,
                Sku = new OperationalInsights.Inputs.WorkspaceSkuArgs
                {
                    Name = "PerGB2018"
                },
                RetentionInDays = 30,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var logAnalyticsWorkspaceEastUs = new OperationalInsights.Workspace(
            $"sleekflow-{LocationNames.EastUs}",
            new OperationalInsights.WorkspaceArgs
            {
                ResourceGroupName = resourceGroup.Name,
                Location = LocationNames.EastUs,
                Sku = new OperationalInsights.Inputs.WorkspaceSkuArgs
                {
                    Name = "PerGB2018"
                },
                RetentionInDays = 30,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var logAnalyticsWorkspaceSouthEastAsia = new OperationalInsights.Workspace(
            $"sleekflow-{LocationNames.SouthEastAsia}",
            new OperationalInsights.WorkspaceArgs
            {
                ResourceGroupName = resourceGroup.Name,
                Location = LocationNames.SouthEastAsia,
                Sku = new OperationalInsights.Inputs.WorkspaceSkuArgs
                {
                    Name = "PerGB2018"
                },
                RetentionInDays = 30,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var logAnalyticsWorkspaceUaeNorth = new OperationalInsights.Workspace(
            $"sleekflow-{LocationNames.UaeNorth}",
            new OperationalInsights.WorkspaceArgs
            {
                ResourceGroupName = resourceGroup.Name,
                Location = LocationNames.UaeNorth,
                Sku = new OperationalInsights.Inputs.WorkspaceSkuArgs
                {
                    Name = "PerGB2018"
                },
                RetentionInDays = 30,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var logAnalyticsWorkspaceWestEurope = new OperationalInsights.Workspace(
            $"sleekflow-{LocationNames.WestEurope}",
            new OperationalInsights.WorkspaceArgs
            {
                ResourceGroupName = resourceGroup.Name,
                Location = LocationNames.WestEurope,
                Sku = new OperationalInsights.Inputs.WorkspaceSkuArgs
                {
                    Name = "PerGB2018"
                },
                RetentionInDays = 30,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var cosmosdbAccount = new DocumentDB.DatabaseAccount(
            "sleekflow",
            new DocumentDB.DatabaseAccountArgs
            {
                ResourceGroupName = resourceGroup.Name,
                DatabaseAccountOfferType = DocumentDB.DatabaseAccountOfferType.Standard,
                Locations =
                {
                    new DocumentDB.Inputs.LocationArgs
                    {
                        LocationName = resourceGroup.Location, FailoverPriority = 0,
                    },
                },
                ConsistencyPolicy = new DocumentDB.Inputs.ConsistencyPolicyArgs
                {
                    DefaultConsistencyLevel = DocumentDB.DefaultConsistencyLevel.Session,
                },
                EnableFreeTier = false,
                EnableAnalyticalStorage = true,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var globalCosmosdbAccount = new DocumentDB.DatabaseAccount(
            "sleekflow-global",
            new DocumentDB.DatabaseAccountArgs
            {
                ResourceGroupName = resourceGroup.Name,
                DatabaseAccountOfferType = DocumentDB.DatabaseAccountOfferType.Standard,
                Locations =
                {
                    new DocumentDB.Inputs.LocationArgs
                    {
                        LocationName = resourceGroup.Location, FailoverPriority = 0,
                    },
                    new DocumentDB.Inputs.LocationArgs
                    {
                        LocationName = LocationNames.GetAzureLocation(LocationNames.EastUs),
                        FailoverPriority = 1,
                        IsZoneRedundant = false
                    },
                    new DocumentDB.Inputs.LocationArgs
                    {
                        LocationName = LocationNames.GetAzureLocation(LocationNames.SouthEastAsia),
                        FailoverPriority = 2,
                        IsZoneRedundant = false
                    },
                    new DocumentDB.Inputs.LocationArgs
                    {
                        LocationName = LocationNames.GetAzureLocation(LocationNames.UaeNorth),
                        FailoverPriority = 3,
                        IsZoneRedundant = false
                    },
                    new DocumentDB.Inputs.LocationArgs
                    {
                        LocationName = LocationNames.GetAzureLocation(LocationNames.WestEurope),
                        FailoverPriority = 4,
                        IsZoneRedundant = false
                    }
                },
                ConsistencyPolicy = new DocumentDB.Inputs.ConsistencyPolicyArgs
                {
                    DefaultConsistencyLevel = DocumentDB.DefaultConsistencyLevel.BoundedStaleness,
                    MaxIntervalInSeconds = 300,
                    MaxStalenessPrefix = 100000
                },
                EnableFreeTier = false,
                EnableAnalyticalStorage = true,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var vectorCosmosdbAccount = new DocumentDB.DatabaseAccount(
            "sleekflow-vector",
            new DocumentDB.DatabaseAccountArgs
            {
                ResourceGroupName = resourceGroup.Name,
                DatabaseAccountOfferType = DocumentDB.DatabaseAccountOfferType.Standard,
                Locations =
                {
                    new DocumentDB.Inputs.LocationArgs
                    {
                        LocationName = resourceGroup.Location, FailoverPriority = 0,
                    },
                },
                ConsistencyPolicy = new DocumentDB.Inputs.ConsistencyPolicyArgs
                {
                    DefaultConsistencyLevel = DocumentDB.DefaultConsistencyLevel.Session,
                },
                Capabilities = new DocumentDB.Inputs.CapabilityArgs[]
                {
                    new CapabilityArgs
                    {
                        Name = "EnableNoSQLVectorSearch"
                    }
                },
                EnableFreeTier = false,
                EnableAnalyticalStorage = false,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var signalR = new SignalRService.SignalR(
            "sleekflow-signalr",
            new SignalRService.SignalRArgs
            {
                ResourceGroupName = resourceGroup.Name,
                Features = new[]
                {
                    new SignalRService.Inputs.SignalRFeatureArgs
                    {
                        Flag = "EnableConnectivityLogs", Value = "True"
                    },
                    new SignalRService.Inputs.SignalRFeatureArgs
                    {
                        Flag = "EnableMessagingLogs", Value = "False"
                    },
                    new SignalRService.Inputs.SignalRFeatureArgs
                    {
                        Flag = "EnableLiveTrace", Value = "False"
                    },
                    new SignalRService.Inputs.SignalRFeatureArgs
                    {
                        Flag = "ServiceMode", Value = "Serverless"
                    },
                },
                Sku = new SignalRService.Inputs.ResourceSkuArgs
                {
                    Capacity = myConfig.Name.ToLower() == "production" ? 4 : 1, Name = "Premium_P1", Tier = "Premium"
                },
                Cors = new SignalRService.Inputs.SignalRCorsSettingsArgs
                {
                    AllowedOrigins = new[]
                    {
                        "*"
                    }
                },
                PublicNetworkAccess = "Enabled",
                Serverless = new SignalRService.Inputs.ServerlessSettingsArgs()
                {
                    ConnectionTimeoutInSeconds = 60,
                },
                Upstream = new SignalRService.Inputs.ServerlessUpstreamSettingsArgs()
                {
                    Templates = new SignalRService.Inputs.UpstreamTemplateArgs()
                    {
                        HubPattern = "*",
                        EventPattern = "*",
                        CategoryPattern = "*",
                        UrlTemplate =
                            Output
                                .Tuple(
                                    stackReference
                                        .GetOutput("FrontDoorEndpoint"),
                                    stackReference
                                        .GetOutput("InternalGatewayApplicationUrl"))
                                .Apply(t =>
                                {
                                    var (frontDoorEndpoint, internalGatewayApplicationUrl) = t;

                                    var frontDoorEndpointStr = frontDoorEndpoint as string;
                                    var internalGatewayApplicationUrlStr = internalGatewayApplicationUrl as string;
                                    var host = string.IsNullOrEmpty(internalGatewayApplicationUrlStr)
                                        ? frontDoorEndpointStr
                                        : internalGatewayApplicationUrlStr;

                                    if (host is not null && host.StartsWith("https://"))
                                    {
                                        host = host["https://".Length..];
                                    }

                                    return
                                        $"https://{(string.IsNullOrEmpty(host) ? "localhost" : host)}/v1/user-event-hub/SignalRWebhook";
                                }),
                        Auth = new SignalRService.Inputs.UpstreamAuthSettingsArgs()
                        {
                            Type = "None"
                        },
                    }
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var _ = new Insights.AutoscaleSetting(
            "sleekflow-signalr-autoscale-setting",
            new Insights.AutoscaleSettingArgs()
            {
                TargetResourceUri = signalR.Id,
                Enabled = true,
                Location = resourceGroup.Location,
                ResourceGroupName = resourceGroup.Name,
                Profiles = new List<Insights.Inputs.AutoscaleProfileArgs>()
                {
                    new ()
                    {
                        Name = "Auto created default scale condition",
                        Capacity = new Insights.Inputs.ScaleCapacityArgs()
                        {
                            Minimum = "1", Maximum = "100", Default = "4"
                        },
                        Rules = new List<Insights.Inputs.ScaleRuleArgs>
                        {
                            new ()
                            {
                                ScaleAction = new Insights.Inputs.ScaleActionArgs()
                                {
                                    Direction = Insights.ScaleDirection.Increase,
                                    Type = Insights.ScaleType.ServiceAllowedNextValue,
                                    Value = "1",
                                    Cooldown = "PT2M"
                                },
                                MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                {
                                    TimeGrain = "PT1M",
                                    Statistic = Insights.MetricStatisticType.Max,
                                    TimeWindow = "PT5M",
                                    TimeAggregation = Insights.TimeAggregationType.Average,
                                    MetricNamespace = "microsoft.signalrservice/signalr",
                                    MetricName = "ConnectionQuotaUtilization",
                                    Operator = Insights.ComparisonOperationType.GreaterThan,
                                    Threshold = 80,
                                    MetricResourceUri = signalR.Id
                                }
                            },
                            new ()
                            {
                                ScaleAction = new Insights.Inputs.ScaleActionArgs
                                {
                                    Cooldown = "PT5M",
                                    Direction = Insights.ScaleDirection.Decrease,
                                    Type = Insights.ScaleType.ServiceAllowedNextValue,
                                    Value = "1",
                                },
                                MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                {
                                    TimeGrain = "PT1M",
                                    Statistic = Insights.MetricStatisticType.Max,
                                    TimeWindow = "PT10M",
                                    TimeAggregation = Insights.TimeAggregationType.Average,
                                    MetricNamespace = "microsoft.signalrservice/signalr",
                                    MetricName = "ConnectionQuotaUtilization",
                                    Operator = Insights.ComparisonOperationType.LessThan,
                                    Threshold = 60,
                                    MetricResourceUri = signalR.Id
                                }
                            },
                        },
                    }
                }
            });

        if (myConfig.Name == "dev")
        {
            new DbScalar(resourceGroup, logAnalyticsWorkspace)
                .InitWorker();
        }

        var dbOutput =
            new Db(resourceGroup, cosmosdbAccount, myConfig)
                .InitDb();
        var synapseOutput =
            new MySynapse(resourceGroup, myConfig)
                .InitSynapse();

        var containerAppManagedEnvAppInsights = new Insights.Component(
            $"sleekflow-container-apps-env-app-insight",
            new Insights.ComponentArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ApplicationType = Insights.ApplicationType.Web,
                FlowType = "Redfield",
                RequestSource = "IbizaAIExtension",
                Kind = "Web",
                WorkspaceResourceId = logAnalyticsWorkspace.Id
            });

        var frontEndAppInsights = new Insights.Component(
            $"sleekflow-fe-app-insight",
            new Insights.ComponentArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ApplicationType = Insights.ApplicationType.Web,
                FlowType = "Redfield",
                RequestSource = "IbizaAIExtension",
                Kind = "Web",
                WorkspaceResourceId = logAnalyticsWorkspace.Id
            });

        // East Asia
        var managedEnvPri =
            new ManagedEnv(resourceGroup, logAnalyticsWorkspace)
                .InitManagedEnv();

        var serviceBusOutput =
            new MyServiceBus(resourceGroup, myConfig).InitServiceBus(
                serviceBusConfigType: ServiceBusConfigTypes.DefaultServiceBus);

        var highTrafficServiceBusOutput =
            new MyServiceBus(resourceGroup, myConfig).InitServiceBus(
                serviceBusConfigType: ServiceBusConfigTypes.HighTrafficServiceBus);
        var redis =
            new Redis(resourceGroup, myConfig).InitRedis();
        var schedulerRedis =
            new Redis(resourceGroup, myConfig).InitRedis("sch");
        var massTransitBlobStorage =
            new MassTransitBlobStorage(myConfig, resourceGroup)
                .InitMassTransitBlobStorage();
        var eventHubEastAsia =
            new MyEventHub(resourceGroup)
                .InitEventHub();

        // East US
        var envNameEastUs = "eus";
        var managedEnvPriEastUs =
            new ManagedEnv(resourceGroup, logAnalyticsWorkspaceEastUs)
                .InitManagedEnv(envNameEastUs, LocationNames.EastUs);

        var serviceBusEastUs =
            new MyServiceBus(resourceGroup, myConfig).InitServiceBus(
                envNameEastUs,
                LocationNames.EastUs,
                ServiceBusConfigTypes.DefaultServiceBus);
        var redisEastUs =
            new Redis(resourceGroup, myConfig).InitRedis(envNameEastUs, LocationNames.EastUs);
        var schedulerRedisEastUs =
            new Redis(resourceGroup, myConfig).InitRedis($"sch-{envNameEastUs}", LocationNames.EastUs);
        var massTransitBlobStorageEastUs =
            new MassTransitBlobStorage(myConfig, resourceGroup)
                .InitMassTransitBlobStorage(envNameEastUs, LocationNames.EastUs);
        var eventHubEastUs =
            new MyEventHub(resourceGroup)
                .InitEventHub(envNameEastUs, LocationNames.EastUs);

        // South East Asia.
        var envNameSouthEastAsia = "seas";
        var managedEnvPriSouthEastAsia =
            new ManagedEnv(resourceGroup, logAnalyticsWorkspaceSouthEastAsia)
                .InitManagedEnv(envNameSouthEastAsia, LocationNames.SouthEastAsia);

        var serviceBusSouthEastAsia =
            new MyServiceBus(resourceGroup, myConfig).InitServiceBus(
                envNameSouthEastAsia,
                LocationNames.SouthEastAsia,
                ServiceBusConfigTypes.DefaultServiceBus);
        var redisSouthEastAsia =
            new Redis(resourceGroup, myConfig).InitRedis(envNameSouthEastAsia, LocationNames.SouthEastAsia);
        var schedulerRedisSouthEastAsia =
            new Redis(resourceGroup, myConfig).InitRedis($"sch-{envNameSouthEastAsia}", LocationNames.SouthEastAsia);
        var massTransitBlobStorageSouthEastAsia =
            new MassTransitBlobStorage(myConfig, resourceGroup).InitMassTransitBlobStorage(
                envNameSouthEastAsia,
                LocationNames.SouthEastAsia);
        var eventHubSouthEastAsia =
            new MyEventHub(resourceGroup)
                .InitEventHub(envNameSouthEastAsia, LocationNames.SouthEastAsia);

        // UAE North
        var envNameUaeNorth = "uaen";
        var managedEnvPriUaeNorth =
            new ManagedEnv(resourceGroup, logAnalyticsWorkspaceUaeNorth)
                .InitManagedEnv(envNameUaeNorth, LocationNames.UaeNorth);
        var serviceBusUaeNorth =
            new MyServiceBus(resourceGroup, myConfig).InitServiceBus(
                envNameUaeNorth,
                LocationNames.UaeNorth,
                ServiceBusConfigTypes.DefaultServiceBus);
        var redisUaeNorth =
            new Redis(resourceGroup, myConfig).InitRedis(envNameUaeNorth, LocationNames.UaeNorth);
        var schedulerRedisUaeNorth =
            new Redis(resourceGroup, myConfig).InitRedis($"sch-{envNameUaeNorth}", LocationNames.UaeNorth);
        var massTransitBlobStorageUaeNorth =
            new MassTransitBlobStorage(myConfig, resourceGroup)
                .InitMassTransitBlobStorage(envNameUaeNorth, LocationNames.UaeNorth);
        var eventHubSouthUaeNorth =
            new MyEventHub(resourceGroup)
                .InitEventHub(envNameUaeNorth, LocationNames.UaeNorth);

        // West Europe
        var envNameWestEurope = "weu";
        var managedEnvPriWestEurope =
            new ManagedEnv(resourceGroup, logAnalyticsWorkspaceWestEurope)
                .InitManagedEnv(envNameWestEurope, LocationNames.WestEurope);
        var serviceBusWestEurope =
            new MyServiceBus(resourceGroup, myConfig).InitServiceBus(
                envNameWestEurope,
                LocationNames.WestEurope,
                ServiceBusConfigTypes.DefaultServiceBus);
        var redisWestEurope =
            new Redis(resourceGroup, myConfig).InitRedis(envNameWestEurope, LocationNames.WestEurope);
        var schedulerRedisWestEurope =
            new Redis(resourceGroup, myConfig).InitRedis($"sch-{envNameWestEurope}", LocationNames.WestEurope);
        var massTransitBlobStorageWestEurope =
            new MassTransitBlobStorage(myConfig, resourceGroup)
                .InitMassTransitBlobStorage(envNameWestEurope, LocationNames.WestEurope);
        var eventHubWestEurope =
            new MyEventHub(resourceGroup)
                .InitEventHub(envNameWestEurope, LocationNames.WestEurope);

        // Setting up global managed env
        // East Asia
        var managedEnvAndAppsTupleEas = new ManagedEnvAndAppsTuple(
            managedEnvPri,
            new Dictionary<string, App.ContainerApp>(),
            new Dictionary<string, WebApp>(),
            containerAppManagedEnvAppInsights,
            logAnalyticsWorkspace,
            "pri",
            LocationNames.EastAsia,
            serviceBusOutput,
            highTrafficServiceBusOutput,
            redis,
            schedulerRedis,
            massTransitBlobStorage,
            eventHubEastAsia);

        // East US
        var managedEnvAndAppsTupleEus = new FilteredManagedEnvAndAppsTuple(
            managedEnvPriEastUs,
            new Dictionary<string, App.ContainerApp>(),
            new Dictionary<string, WebApp>(),
            containerAppManagedEnvAppInsights,
            logAnalyticsWorkspaceEastUs,
            envNameEastUs,
            LocationNames.EastUs,
            serviceBusEastUs,
            null,
            redisEastUs,
            schedulerRedisEastUs,
            massTransitBlobStorageEastUs,
            eventHubEastUs,
            new List<string>
            {
                ServiceNames.TenantHub,
                ServiceNames.OpenPolicyAgent,
                ServiceNames.OpenPolicyAdministrationLayer,
                ServiceNames.ShareHub
            });

        // South East Asia
        var managedEnvAndAppsTupleSeas = new FilteredManagedEnvAndAppsTuple(
            managedEnvPriSouthEastAsia,
            new Dictionary<string, App.ContainerApp>(),
            new Dictionary<string, WebApp>(),
            containerAppManagedEnvAppInsights,
            logAnalyticsWorkspaceSouthEastAsia,
            envNameSouthEastAsia,
            LocationNames.SouthEastAsia,
            serviceBusSouthEastAsia,
            null,
            redisSouthEastAsia,
            schedulerRedisSouthEastAsia,
            massTransitBlobStorageSouthEastAsia,
            eventHubSouthEastAsia,
            new List<string>
            {
                ServiceNames.TenantHub,
                ServiceNames.OpenPolicyAgent,
                ServiceNames.OpenPolicyAdministrationLayer,
                ServiceNames.ShareHub
            });

        // West Europe
        var managedEnvAndAppsTupleWeu = new FilteredManagedEnvAndAppsTuple(
            managedEnvPriWestEurope,
            new Dictionary<string, App.ContainerApp>(),
            new Dictionary<string, WebApp>(),
            containerAppManagedEnvAppInsights,
            logAnalyticsWorkspaceWestEurope,
            envNameWestEurope,
            LocationNames.WestEurope,
            serviceBusWestEurope,
            null,
            redisWestEurope,
            schedulerRedisWestEurope,
            massTransitBlobStorageWestEurope,
            eventHubWestEurope,
            new List<string>
            {
                ServiceNames.TenantHub,
                ServiceNames.OpenPolicyAgent,
                ServiceNames.OpenPolicyAdministrationLayer,
                ServiceNames.ShareHub
            });

        // UAE North
        var managedEnvAndAppsTupleUaen = new FilteredManagedEnvAndAppsTuple(
            managedEnvPriUaeNorth,
            new Dictionary<string, App.ContainerApp>(),
            new Dictionary<string, WebApp>(),
            containerAppManagedEnvAppInsights,
            logAnalyticsWorkspaceUaeNorth,
            envNameUaeNorth,
            LocationNames.UaeNorth,
            serviceBusUaeNorth,
            null,
            redisUaeNorth,
            schedulerRedisUaeNorth,
            massTransitBlobStorageUaeNorth,
            eventHubSouthUaeNorth,
            new List<string>
            {
                ServiceNames.TenantHub,
                ServiceNames.OpenPolicyAgent,
                ServiceNames.OpenPolicyAdministrationLayer,
                ServiceNames.ShareHub
            });

        var managedEnvAndAppsTuples = new List<ManagedEnvAndAppsTuple>()
        {
            managedEnvAndAppsTupleEas,
            managedEnvAndAppsTupleEus,
            managedEnvAndAppsTupleSeas,
            managedEnvAndAppsTupleWeu,
            managedEnvAndAppsTupleUaen
        };

        var (_, _, _, _, _, _, crmHubDbId) = InitCrmHub(
            resourceGroup,
            cosmosdbAccount,
            configurationStore,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig);
        var (_, messageHubDbId) = InitMessagingHub(
            resourceGroup,
            cosmosdbAccount,
            configurationStore,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig);
        var (_, auditHubDbId) = InitAuditHub(
            resourceGroup,
            cosmosdbAccount,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig);
        var (_, commerceHubDbId) = InitCommerceHub(
            resourceGroup,
            cosmosdbAccount,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            configurationStore,
            gcpConfig);
        var (_, emailHubDbId) = InitEmailHub(
            resourceGroup,
            cosmosdbAccount,
            configurationStore,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig);
        var (_, _, flowHubDatabaseId) = InitFlowHub(
            resourceGroup,
            cosmosdbAccount,
            configurationStore,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig);
        var (_, shareHubDbId) = InitShareHub(
            resourceGroup,
            cosmosdbAccount,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            synapseOutput,
            gcpConfig);
        InitPublicApiGateway(
            myConfig,
            dbOutput,
            resourceGroup,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            registry,
            cosmosdbAccount,
            gcpConfig);
        var (_, intelligentHubDbId) = InitIntelligentHub(
            resourceGroup,
            cosmosdbAccount,
            configurationStore,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig,
            vectorCosmosdbAccount);
        InitJourneyBuilderCustomActivity(
            resourceGroup,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            myConfig);
        var (_, userEventHubDbId) = InitUserEventHub(
            resourceGroup,
            cosmosdbAccount,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            signalR,
            gcpConfig);
        var (_, supportHubDbId) = InitSupportHub(
            resourceGroup,
            cosmosdbAccount,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig);
        var (_, ticketingHubDbId) = InitTicketingHub(
            resourceGroup,
            cosmosdbAccount,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig);
        var (_, internalIntegrationHubDbId) = InitInternalIntegrationHub(
            resourceGroup,
            cosmosdbAccount,
            configurationStore,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig,
            coreSqlDbConfig);
        (Dictionary<string, App.ContainerApp?>? Apps, StorageAccount? StorageAccount) rbacApps =
            InitOpa(
                registry,
                adminUsername,
                adminPassword,
                resourceGroup,
                managedEnvAndAppsTuples,
                myConfig,
                gcpConfig);
        var (_, tenantHubDbId) = InitTenantHub(
            resourceGroup,
            globalCosmosdbAccount,
            registry,
            adminUsername,
            adminPassword,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig,
            rbacApps);
        new WebhookHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig).InitWebhookHub();

        // Create VNet-enabled infrastructure for WebhookBridge with static outbound IP addresses

        // East Asia
        var webhookBridgeVNetOutputEas = new WebhookBridgeVNet(resourceGroup, LocationNames.EastAsia, myConfig)
            .InitWebhookBridgeVNet();

        var webhookBridgeVNetManagedEnvEas = new VNetManagedEnv(
                resourceGroup,
                logAnalyticsWorkspace,
                webhookBridgeVNetOutputEas)
            .InitVNetManagedEnv(LocationNames.GetShortName(LocationNames.EastAsia), LocationNames.EastAsia);

        var webhookBridgeEas = new WebhookBridge(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            myConfig,
            LocationNames.EastAsia,
            webhookBridgeVNetManagedEnvEas,
            dbOutput,
            redis,
            serviceBusOutput,
            eventHubEastAsia,
            massTransitBlobStorage).InitWebhookBridge();

        new Scheduler(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            gcpConfig).InitScheduler();
        new ApiGateway(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            myConfig,
            managedEnvAndAppsTuples).InitApiGateway();
        var internalGateway = new InternalGateway(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            myConfig,
            managedEnvAndAppsTuples).InitInternalGateway();
        var frontDoorEndpoint =
            new FrontDoor(resourceGroup, managedEnvAndAppsTuples, myConfig, auth0Config).InitFrontDoor();

        var (criticalActionGroup, preventionActionGroup) = InitActionGroups(resourceGroup, myConfig);
        var cosmosDbIds = new List<string>
        {
            flowHubDatabaseId,
            auditHubDbId,
            commerceHubDbId,
            crmHubDbId,
            intelligentHubDbId,
            messageHubDbId,
            shareHubDbId,
            supportHubDbId,
            ticketingHubDbId,
            userEventHubDbId,
            internalIntegrationHubDbId
        };

        _flowHubDatabaseId = flowHubDatabaseId;
        _auditHubDbId = auditHubDbId;
        _commerceHubDbId = commerceHubDbId;
        _crmHubDbId = crmHubDbId;
        _intelligentHubDbId = intelligentHubDbId;
        _messageHubDbId = messageHubDbId;
        _shareHubDbId = shareHubDbId;
        _supportHubDbId = supportHubDbId;
        _ticketingHubDbId = ticketingHubDbId;
        _userEventHubDbId = userEventHubDbId;
        _internalIntegrationHubDbId = internalIntegrationHubDbId;

        InitAlertRules(
            resourceGroup,
            myConfig,
            managedEnvAndAppsTuples,
            cosmosdbAccount,
            globalCosmosdbAccount,
            criticalActionGroup,
            preventionActionGroup,
            cosmosDbIds);

        #region CALLBACK_URL

#pragma warning disable S1848
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-SALESFORCE_OAUTH_CALLBACK_URL",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "SALESFORCE_OAUTH_CALLBACK_URL",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/salesforce-integrator/AuthenticateCallback"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-SALESFORCE_WEBHOOK_CALLBACK_URL",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "SALESFORCE_WEBHOOK_CALLBACK_URL",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/salesforce-integrator/ApexCallback"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-HUBSPOT_OAUTH_CALLBACK_URL",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "HUBSPOT_OAUTH_CALLBACK_URL",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/hubspot-integrator/AuthenticateCallback"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-DYNAMICS365_OAUTH_CALLBACK_URL",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "DYNAMICS365_OAUTH_CALLBACK_URL",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/dynamics365-integrator/AuthenticateCallback"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-GOOGLE_SHEETS_OAUTH_CALLBACK_URL",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "GOOGLE_SHEETS_OAUTH_CALLBACK_URL",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/google-sheets-integrator/AuthenticateCallback"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-ZOHO_OAUTH_CALLBACK_URL",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "ZOHO_OAUTH_CALLBACK_URL",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/zoho-integrator/AuthenticateCallback"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
#pragma warning restore S1848

        #endregion

        #region INTERNAL_ENDPOINTS

#pragma warning disable S1848
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-HUBSPOT_INTEGRATOR_INTERNALS_ENDPOINT",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "HUBSPOT_INTEGRATOR_INTERNALS_ENDPOINT",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/hubspot-integrator/internals"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-SALESFORCE_INTEGRATOR_INTERNALS_ENDPOINT",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "SALESFORCE_INTEGRATOR_INTERNALS_ENDPOINT",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/salesforce-integrator/internals"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-DYNAMICS365_INTEGRATOR_INTERNALS_ENDPOINT",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "DYNAMICS365_INTEGRATOR_INTERNALS_ENDPOINT",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/dynamics365-integrator/internals"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-GOOGLE_SHEETS_INTEGRATOR_INTERNALS_ENDPOINT",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "GOOGLE_SHEETS_INTEGRATOR_INTERNALS_ENDPOINT",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/google-sheets-integrator/internals"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-ZOHO_INTEGRATOR_INTERNALS_ENDPOINT",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "ZOHO_INTEGRATOR_INTERNALS_ENDPOINT",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/zoho-integrator/internals"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-COMMERCE_HUB_INTERNALS_ENDPOINT",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "COMMERCE_HUB_INTERNALS_ENDPOINT",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/commerce-hub/internals"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-FLOW_HUB_INTERNALS_ENDPOINT",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "FLOW_HUB_INTERNALS_ENDPOINT",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/flow-hub/internals"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-INTELLIGENT_HUB_INTERNALS_ENDPOINT",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "INTELLIGENT_HUB_INTERNALS_ENDPOINT",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/intelligent-hub/internals"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
        new AppConfiguration.KeyValue(
            "sleekflow-app-configuration-CRM_HUB_INTERNALS_ENDPOINT",
            new AppConfiguration.KeyValueArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ConfigStoreName = configurationStore.Name,
                KeyValueName = "CRM_HUB_INTERNALS_ENDPOINT",
                Value = frontDoorEndpoint.HostName.Apply(hn =>
                    "https://" + hn + "/v1/crm-hub/internals"),
            },
            new CustomResourceOptions
            {
                Parent = configurationStore
            });
#pragma warning restore S1848

        #endregion

        this.FrontDoorEndpoint =
            frontDoorEndpoint.HostName;

        this.InternalGatewayApplicationUrl =
            Output.Unsecret(internalGateway.GetValueOrDefault(LocationNames.EastAsia).ApplicationUrl);

        new AzurePortalDashboard(myConfig, resourceGroup).InitPortalDashboard();
    }

    private static (Insights.ActionGroup criticalActionGroup, Insights.ActionGroup preventionActionGroup)
        InitActionGroups(
            ResourceGroup resourceGroup,
            MyConfig myConfig)
    {
        var criticalActionGroup = new Insights.ActionGroup(
            $"sleekflow-critical-action-group",
            new Insights.ActionGroupArgs()
            {
                Enabled = true,
                GroupShortName = "critical",
                Location = "Global",
                ResourceGroupName = resourceGroup.Name,
                ActionGroupName = "critical-action-group",
                EmailReceivers = new InputList<EmailReceiverArgs>(),
                SmsReceivers = new InputList<SmsReceiverArgs>(),
                WebhookReceivers = myConfig.Name.ToLower() == "production"
                    ? new[]
                    {
                        new Insights.Inputs.WebhookReceiverArgs
                        {
                            ServiceUri =
                                "https://api.opsgenie.com/v1/json/azure?apiKey=************************************",
                            Name = "Opsgenie - Engineering",
                            UseCommonAlertSchema = true,
                            UseAadAuth = false
                        }
                    }
                    : new List<Insights.Inputs.WebhookReceiverArgs>()
            });

        var preventionActionGroup = new Insights.ActionGroup(
            $"sleekflow-prevention-action-group",
            new Insights.ActionGroupArgs()
            {
                Enabled = true,
                GroupShortName = "prevention",
                Location = "Global",
                ResourceGroupName = resourceGroup.Name,
                ActionGroupName = "prevention-action-group",
                EmailReceivers = new InputList<EmailReceiverArgs>(),
                SmsReceivers = new InputList<SmsReceiverArgs>(),
                WebhookReceivers = myConfig.Name.ToLower() == "production"
                    ? new[]
                    {
                        new Insights.Inputs.WebhookReceiverArgs
                        {
                            ServiceUri =
                                "https://api.opsgenie.com/v1/json/azure?apiKey=************************************",
                            Name = "Opsgenie - Engineering",
                            UseCommonAlertSchema = true,
                            UseAadAuth = false
                        }
                    }
                    : new List<Insights.Inputs.WebhookReceiverArgs>()
            });
        return (criticalActionGroup, preventionActionGroup);
    }

    private static void InitAlertRules(
        ResourceGroup resourceGroup,
        MyConfig myConfig,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        DocumentDB.DatabaseAccount cosmosDbAccount,
        DocumentDB.DatabaseAccount globalCosmosDbAccount,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup,
        List<string> cosmosDbIds)
    {
        if (!myConfig.Name.Equals("production", StringComparison.CurrentCultureIgnoreCase))
        {
            return;
        }

        managedEnvAndAppsTuples.ForEach(managedEnvAndAppsTuple =>
        {
            if (myConfig.Name.ToLower() == "production")
            {
                // production-only metrics
                InitAlertRulesForServiceBusCpuPercentage(
                    resourceGroup,
                    managedEnvAndAppsTuple,
                    criticalActionGroup,
                    preventionActionGroup);
                InitAlertRulesForHighTrafficServiceBusCpuPercentage(
                    resourceGroup,
                    managedEnvAndAppsTuple,
                    criticalActionGroup,
                    preventionActionGroup);
                InitAlertRulesForServiceBusMemoryPercentage(
                    resourceGroup,
                    managedEnvAndAppsTuple,
                    criticalActionGroup,
                    preventionActionGroup);
                InitAlertRulesForHighTrafficServiceBusMemoryPercentage(
                    resourceGroup,
                    managedEnvAndAppsTuple,
                    criticalActionGroup,
                    preventionActionGroup);
                InitAlertRulesForSchedulerRedisMemoryPercentage(
                    resourceGroup,
                    managedEnvAndAppsTuple,
                    criticalActionGroup,
                    preventionActionGroup);
                InitAlertRulesForSchedulerRedisLatency(
                    resourceGroup,
                    managedEnvAndAppsTuple,
                    criticalActionGroup,
                    preventionActionGroup);
            }

            InitAlertRulesForContainerAppReplicaRestartCount(
                resourceGroup,
                managedEnvAndAppsTuple,
                criticalActionGroup,
                preventionActionGroup);
            InitAlertRulesForRedisMemoryPercentage(
                resourceGroup,
                managedEnvAndAppsTuple,
                criticalActionGroup,
                preventionActionGroup);
            InitAlertRulesForRedisLatency(
                resourceGroup,
                managedEnvAndAppsTuple,
                criticalActionGroup,
                preventionActionGroup);
            InitAlertRulesForServiceBusThrottledRequests(
                resourceGroup,
                managedEnvAndAppsTuple,
                criticalActionGroup,
                preventionActionGroup);
            InitAlertRulesForHighTrafficServiceBusThrottledRequests(
                resourceGroup,
                managedEnvAndAppsTuple,
                criticalActionGroup,
                preventionActionGroup);
        });

        InitAlertRulesForDbServiceAvailability(
            resourceGroup,
            cosmosDbAccount,
            globalCosmosDbAccount,
            criticalActionGroup,
            preventionActionGroup);

        InitAlertRulesForDbThrottling(
            resourceGroup,
            cosmosDbAccount,
            globalCosmosDbAccount,
            criticalActionGroup,
            preventionActionGroup,
            cosmosDbIds);
    }

    private static void InitAlertRulesForContainerAppReplicaRestartCount(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        foreach (var (containerAppName, containerApp) in managedEnvAndAppsTuple.ContainerApps)
        {
            var criticalReplicaRestartCountMetricAlert = new Insights.MetricAlert(
                $"sleekflow-{ServiceNames.GetSleekflowPrefixedShortName(containerAppName)}-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-replica-restart-count-over-5-in-15-minutes",
                new Insights.MetricAlertArgs
                {
                    RuleName =
                        $"P1 (sleekflow-{ServiceNames.GetSleekflowPrefixedShortName(containerAppName)}-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Replica restart count is over 5 in 5 minutes",
                    ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                    Location = "global", // Use the location as specified in the ARM template
                    Severity = 1,
                    Enabled = true,
                    Scopes =
                    {
                        containerApp.Id
                    },
                    EvaluationFrequency = "PT1M",
                    WindowSize = "PT5M",
                    Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                    {
                        AllOf =
                        {
                            new Insights.Inputs.MetricCriteriaArgs
                            {
                                Threshold = 5,
                                Name = "Metric1",
                                MetricNamespace = "microsoft.app/containerapps",
                                MetricName = "RestartCount",
                                Operator = Insights.Operator.GreaterThan,
                                TimeAggregation = Insights.AggregationTypeEnum.Total,
                                SkipMetricValidation = false,
                                CriterionType = "StaticThresholdCriterion",
                            }
                        },
                        OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                    },
                    AutoMitigate = true,
                    TargetResourceType = "Microsoft.App/containerapps",
                    TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                    Actions = new Insights.Inputs.MetricAlertActionArgs
                    {
                        ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });
        }
    }

    private static void InitAlertRulesForRedisMemoryPercentage(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        var criticalRedisMemoryPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-redis-avg-memory-percentage-over-90-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Redis used memory percentage is over 90 for 15 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.Redis.Id,
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 90,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.cache/redis",
                            MetricName = "usedmemorypercentage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Cache/redis",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionRedisMemoryPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-redis-avg-memory-percentage-over-80-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Redis used memory percentage is over 80 for 15 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.Redis.Id,
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.cache/redis",
                            MetricName = "usedmemorypercentage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Cache/redis",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = preventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForRedisLatency(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        var criticalRedisLatencyMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-redis-cache-latency-over-100ms-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Redis cache latency is over 100ms for 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.Redis.Id,
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 100000,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.cache/redis",
                            MetricName = "cacheLatency",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Cache/redis",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionRedisLatencyMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-redis-cache-latency-over-50ms-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Redis cache latency is over 50ms for 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.Redis.Id,
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 50000,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.cache/redis",
                            MetricName = "cacheLatency",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Cache/redis",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = preventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForSchedulerRedisMemoryPercentage(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        var criticalSchedulerRedisMemoryPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-scheduler-redis-avg-memory-percentage-over-90-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Scheduler redis used memory percentage is over 90 for 15 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.SchedulerRedis.Id,
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 90,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.cache/redis",
                            MetricName = "usedmemorypercentage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Cache/redis",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionSchedulerRedisMemoryPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-scheduler-redis-avg-memory-percentage-over-80-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Scheduler redis used memory percentage is over 80 for 15 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.SchedulerRedis.Id,
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.cache/redis",
                            MetricName = "usedmemorypercentage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Cache/redis",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = preventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForSchedulerRedisLatency(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        var criticalSchedulerRedisLatencyMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-scheduler-redis-cache-latency-over-100ms-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Scheduler redis cache latency is over 100ms for 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.SchedulerRedis.Id,
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 100000,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.cache/redis",
                            MetricName = "cacheLatency",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Cache/redis",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionSchedulerRedisLatencyMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-scheduler-redis-cache-latency-over-50ms-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Scheduler redis cache latency is over 50ms for 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.SchedulerRedis.Id,
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 50000,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.cache/redis",
                            MetricName = "cacheLatency",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Cache/redis",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = preventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForServiceBusCpuPercentage(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        var criticalServiceBusCpuPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-service-bus-avg-cpu-percentage-over-90-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Service bus namespace average cpu percentage is over 90 for 15 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.ServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 90,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "NamespaceCpuUsage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionServiceBusCpuPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-service-bus-avg-cpu-percentage-over-80-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Service bus namespace average cpu percentage is over 80 for 15 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.ServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "NamespaceCpuUsage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = preventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForHighTrafficServiceBusCpuPercentage(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        if (managedEnvAndAppsTuple.HighTrafficServiceBus == null)
        {
            return;
        }

        var criticalServiceBusCpuPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-high-traffic-service-bus-avg-cpu-percentage-over-90-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - High traffic service bus namespace average cpu percentage is over 90 for 15 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.HighTrafficServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 90,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "NamespaceCpuUsage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Maximum,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionServiceBusCpuPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-high-traffic-service-bus-avg-cpu-percentage-over-80-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - High traffic service bus namespace average cpu percentage is over 80 for 15 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.HighTrafficServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "NamespaceCpuUsage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Maximum,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = preventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForServiceBusMemoryPercentage(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        var criticalServiceBusMemoryPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-service-bus-avg-memory-percentage-over-90-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Service bus namespace average memory percentage is over 90 for 15 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.ServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 90,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "NamespaceMemoryUsage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionServiceBusMemoryPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-service-bus-avg-memory-percentage-over-80-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Service bus namespace average memory percentage is over 80 for 15 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.ServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "NamespaceMemoryUsage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = preventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForHighTrafficServiceBusMemoryPercentage(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        if (managedEnvAndAppsTuple.HighTrafficServiceBus == null)
        {
            return;
        }

        var criticalServiceBusMemoryPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-high-traffic-service-bus-avg-memory-percentage-over-90-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - High traffic service bus namespace average memory percentage is over 90 for 15 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.HighTrafficServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 90,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "NamespaceMemoryUsage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionServiceBusMemoryPercentageMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-high-traffic-service-bus-avg-memory-percentage-over-80-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - High traffic service bus namespace average memory percentage is over 80 for 15 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.HighTrafficServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "NamespaceMemoryUsage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = preventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForServiceBusThrottledRequests(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        var criticalServiceBusThrottledRequestsMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-service-bus-throttled-requests-over-100-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Service bus throttled requests is over 100 for 5 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.ServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 100,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "ThrottledRequests",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Total,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionServiceBusThrottledRequestsMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-service-bus-throttled-requests-over-50-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - Service bus namespace throttled requests is over 50 for 5 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.ServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 50,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "ThrottledRequests",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Total,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = preventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForHighTrafficServiceBusThrottledRequests(
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        if (managedEnvAndAppsTuple.HighTrafficServiceBus == null)
        {
            return;
        }

        var criticalServiceBusThrottledRequestsMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-high-traffic-service-bus-throttled-requests-over-100-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - High traffic service bus throttled requests is over 100 for 5 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.HighTrafficServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 100,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "ThrottledRequests",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Total,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionServiceBusThrottledRequestsMetricAlert = new Insights.MetricAlert(
            $"sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}-high-traffic-service-bus-throttled-requests-over-50-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 (sleekflow-{LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName)}) - High traffic service bus namespace throttled requests is over 50 for 5 mins",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    managedEnvAndAppsTuple.HighTrafficServiceBus.ResourceId
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 50,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.serviceBus/Namespaces",
                            MetricName = "ThrottledRequests",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Total,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.ServiceBus/Namespaces",
                TargetResourceRegion = managedEnvAndAppsTuple.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = preventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForDbServiceAvailability(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosDbAccount,
        DocumentDB.DatabaseAccount globalCosmosDbAccount,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup)
    {
        var criticalCosmosDbServiceAvailabilityMetricAlert = new Insights.MetricAlert(
            $"sleekflow-cosmos-db-availability-below-80-for-1-hour",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow) - CosmosDb service availability below 80 for 1 hour",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    cosmosDbAccount.Id
                },
                EvaluationFrequency = "PT1H",
                WindowSize = "PT1H",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "Microsoft.DocumentDB/DatabaseAccounts",
                            MetricName = "ServiceAvailability",
                            Operator = Insights.Operator.LessThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.DocumentDB/DatabaseAccounts",
                TargetResourceRegion = resourceGroup.Location,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var criticalGlobalCosmosDbServiceAvailabilityMetricAlert = new Insights.MetricAlert(
            $"sleekflow-global-cosmos-db-availability-below-80-for-1-hour",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 (sleekflow) - Global cosmosDb service availability below 80 for 1 hour",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    globalCosmosDbAccount.Id
                },
                EvaluationFrequency = "PT1H",
                WindowSize = "PT1H",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "Microsoft.DocumentDB/DatabaseAccounts",
                            MetricName = "ServiceAvailability",
                            Operator = Insights.Operator.LessThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.DocumentDB/DatabaseAccounts",
                TargetResourceRegion = resourceGroup.Location,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForDbThrottling(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosDbAccount,
        DocumentDB.DatabaseAccount globalCosmosDbAccount,
        Insights.ActionGroup criticalActionGroup,
        Insights.ActionGroup preventionActionGroup,
        List<string> cosmosDbIds)
    {
        Insights.MetricAlert GenerateDbThrottlingAlertTemplate(
            string resourceName,
            string ruleName,
            int threshold,
            DocumentDB.DatabaseAccount dbAccount,
            InputList<MetricDimensionArgs> dimensions)
        {
            return new Insights.MetricAlert(
                resourceName,
                new Insights.MetricAlertArgs
                {
                    RuleName =
                        ruleName,
                    ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                    Location = "global", // Use the location as specified in the ARM template
                    Severity = 1,
                    Enabled = true,
                    Scopes =
                    {
                        dbAccount.Id
                    },
                    EvaluationFrequency = "PT5M",
                    WindowSize = "PT5M",
                    Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                    {
                        AllOf =
                        {
                            new Insights.Inputs.MetricCriteriaArgs
                            {
                                Threshold = threshold,
                                Name = "Metric1",
                                MetricNamespace = "Microsoft.DocumentDB/DatabaseAccounts",
                                MetricName = "TotalRequests",
                                Dimensions = dimensions,
                                Operator = Insights.Operator.GreaterThan,
                                TimeAggregation = Insights.AggregationTypeEnum.Count,
                                SkipMetricValidation = false,
                                CriterionType = "StaticThresholdCriterion",
                            }
                        },
                        OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                    },
                    AutoMitigate = true,
                    TargetResourceType = "Microsoft.DocumentDB/DatabaseAccounts",
                    TargetResourceRegion = resourceGroup.Location,
                    Actions = new Insights.Inputs.MetricAlertActionArgs
                    {
                        ActionGroupId = criticalActionGroup.Id // The action group to invoke when the alert fires
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });
        }


        const string emptyDatabaseName = "<empty>";
        foreach (var cosmosDbId in cosmosDbIds)
        {
            if (cosmosDbId.Equals(_crmHubDbId))
            {
                continue;
            }

            var _ = GenerateDbThrottlingAlertTemplate(
                $"cosmos-{cosmosDbId}-throttled-200-for-5-min",
                $"P1 (CosmosDb) {cosmosDbId} has been throttled more than 200 times for 5 min",
                200,
                cosmosDbAccount,
                new InputList<MetricDimensionArgs>
                {
                    new Insights.Inputs.MetricDimensionArgs
                    {
                        Name = "StatusCode",
                        Operator = "Include",
                        Values =
                        {
                            "429"
                        }
                    },
                    new Insights.Inputs.MetricDimensionArgs
                    {
                        Name = "DatabaseName",
                        Operator = "Include",
                        Values =
                        {
                            cosmosDbId
                        }
                    },
                    new Insights.Inputs.MetricDimensionArgs
                    {
                        Name = "CollectionName",
                        Operator = "Exclude",
                        Values =
                        {
                            "step_execution"
                        }
                    }
                });
        }

        var stepExecutionCosmosDbThrottleMetricAlert = GenerateDbThrottlingAlertTemplate(
            $"cosmos-{_crmHubDbId}-throttled-200k-for-5-min",
            $"P1 (CosmosDb) {_crmHubDbId} has been throttled more than 200k times for 5-min",
            200000,
            cosmosDbAccount,
            new InputList<MetricDimensionArgs>
            {
                new Insights.Inputs.MetricDimensionArgs
                {
                    Name = "StatusCode",
                    Operator = "Include",
                    Values =
                    {
                        "429"
                    }
                },
                new Insights.Inputs.MetricDimensionArgs
                {
                    Name = "DatabaseName",
                    Operator = "Include",
                    Values =
                    {
                        _crmHubDbId
                    }
                },
                new Insights.Inputs.MetricDimensionArgs
                {
                    Name = "CollectionName",
                    Operator = "Exclude",
                    Values =
                    {
                        "Entity"
                    }
                }
            });

        var crmHubDbThrottleMetricAlert = GenerateDbThrottlingAlertTemplate(
            $"cosmos-onStepExecution-throttled-200k-for-5-min",
            $"P1 (CosmosDb) onStepExecution has been throttled more than 200k times for 5 min",
            200000,
            cosmosDbAccount,
            new InputList<MetricDimensionArgs>
            {
                new Insights.Inputs.MetricDimensionArgs
                {
                    Name = "StatusCode",
                    Operator = "Include",
                    Values =
                    {
                        "429"
                    }
                },
                new Insights.Inputs.MetricDimensionArgs
                {
                    Name = "DatabaseName",
                    Operator = "Include",
                    Values =
                    {
                        _flowHubDatabaseId
                    }
                },
                new Insights.Inputs.MetricDimensionArgs
                {
                    Name = "CollectionName",
                    Operator = "Include",
                    Values =
                    {
                        "step_execution"
                    }
                }
            });

        var criticalGlobalCosmosDbThrottleMetricAlert = GenerateDbThrottlingAlertTemplate(
            $"sleekflow-global-cosmos-db-throttled-10-for-5-min",
            $"P1 (sleekflow) - Global cosmosDb has been throttled more than 10 times for 5-min",
            10,
            globalCosmosDbAccount,
            new InputList<MetricDimensionArgs>
            {
                new Insights.Inputs.MetricDimensionArgs
                {
                    Name = "StatusCode",
                    Operator = "Include",
                    Values =
                    {
                        "429"
                    }
                }
            });

        cosmosDbIds.Add(emptyDatabaseName);

        var criticalNonCoreCosmosDbThrottleMetricAlert = GenerateDbThrottlingAlertTemplate(
            $"cosmos-non-core-throttled-200-for-5-min",
            $"P1 non core CosmosDb has been throttled more than 200 times for 5-min",
            10,
            cosmosDbAccount,
            new InputList<MetricDimensionArgs>
            {
                {
                    new Insights.Inputs.MetricDimensionArgs
                    {
                        Name = "StatusCode",
                        Operator = "Include",
                        Values =
                        {
                            "429"
                        }
                    },
                    new Insights.Inputs.MetricDimensionArgs
                    {
                        Name = "DatabaseName", Operator = "Exclude", Values = cosmosDbIds
                    }
                }
            });
    }

    private static (
        List<App.ContainerApp> SalesforceIntegratorApps,
        List<App.ContainerApp> HubspotIntegratorApps,
        List<App.ContainerApp> Dynamics365IntegratorApps,
        List<App.ContainerApp> GoogleSheetsIntegratorApps,
        List<App.ContainerApp> ZohoIntegratorApps,
        List<App.ContainerApp> CrmHubApps,
        string DatabaseId) InitCrmHub(
            ResourceGroup resourceGroup,
            DocumentDB.DatabaseAccount cosmosdbAccount,
            AppConfiguration.ConfigurationStore configurationStore,
            ContainerRegistry.Registry registry,
            Output<string> adminUsername,
            Output<string> adminPassword,
            List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
            Db.DbOutput dbOutput,
            MyConfig myConfig,
            GcpConfig gcpConfig)
    {
        var crmHubDbOutput = new CrmHubDb(resourceGroup, cosmosdbAccount, myConfig).InitCrmHubDb();
        var crmHubIntegrationDbOutput =
            new CrmHubIntegrationDb(resourceGroup, cosmosdbAccount, myConfig).InitCrmHubIntegrationDb();
        new CrmHubWorker(
            resourceGroup,
            crmHubDbOutput,
            myConfig,
            configurationStore,
            dbOutput,
            crmHubIntegrationDbOutput,
            managedEnvAndAppsTuples,
            gcpConfig).InitWorker();
        var salesforceIntegratorApps = new SalesforceIntegrator(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            crmHubDbOutput,
            managedEnvAndAppsTuples,
            configurationStore,
            dbOutput,
            myConfig,
            crmHubIntegrationDbOutput,
            gcpConfig).InitSalesforceIntegrator();
        var hubspotIntegratorApps = new HubspotIntegrator(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            crmHubDbOutput,
            managedEnvAndAppsTuples,
            configurationStore,
            dbOutput,
            myConfig,
            crmHubIntegrationDbOutput,
            gcpConfig).InitHubspotIntegrator();
        var dynamics365IntegratorApps = new Dynamics365Integrator(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            crmHubDbOutput,
            managedEnvAndAppsTuples,
            configurationStore,
            dbOutput,
            myConfig,
            crmHubIntegrationDbOutput,
            gcpConfig).InitDynamics365Integrator();
        var googleSheetsIntegratorApps = new GoogleSheetsIntegrator(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            crmHubDbOutput,
            managedEnvAndAppsTuples,
            configurationStore,
            dbOutput,
            myConfig,
            crmHubIntegrationDbOutput,
            gcpConfig).InitGoogleSheetsIntegrator();
        var zohoIntegratorApps = new ZohoIntegrator(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            crmHubDbOutput,
            managedEnvAndAppsTuples,
            configurationStore,
            dbOutput,
            myConfig,
            crmHubIntegrationDbOutput,
            gcpConfig).InitZohoIntegrator();
        var crmHubApps = new CrmHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            crmHubDbOutput,
            managedEnvAndAppsTuples,
            dbOutput,
            myConfig,
            crmHubIntegrationDbOutput,
            gcpConfig).InitCrmHub();
        return (salesforceIntegratorApps, hubspotIntegratorApps, dynamics365IntegratorApps, googleSheetsIntegratorApps,
            zohoIntegratorApps, crmHubApps, crmHubDbOutput.DatabaseId);
    }

    private static (List<App.ContainerApp> App, string DatabaseId) InitCommerceHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        AppConfiguration.ConfigurationStore configurationStore,
        GcpConfig gcpConfig)
    {
        var commerceHubDb = new CommerceHubDb(resourceGroup, cosmosdbAccount, myConfig).InitCommerceHubDb();
        new CommerceHubWorker(
            resourceGroup,
            commerceHubDb,
            configurationStore,
            managedEnvAndAppsTuples,
            dbOutput,
            gcpConfig).InitWorker();
        var commerceHubSearch = new CommerceHubSearch(resourceGroup).InitCommerceHubSearch();
        var commerceHubApps = new Components.CommerceHub.CommerceHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            commerceHubDb,
            myConfig,
            commerceHubSearch,
            gcpConfig).InitCommerceHub();
        return (commerceHubApps, commerceHubDb.DatabaseId);
    }

    private static (List<App.ContainerApp> App, string DatabaseId) InitShareHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        MySynapse.SynapseOutput synapseOutput,
        GcpConfig gcpConfig)
    {
        var shareHubDb = new ShareHubDb(resourceGroup, cosmosdbAccount, myConfig).InitShareHubDb();
        var shareHubApps = new Components.ShareHub.ShareHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            shareHubDb,
            myConfig,
            synapseOutput,
            gcpConfig).InitShareHub();
        return (shareHubApps, shareHubDb.DatabaseId);
    }

    private static (List<App.ContainerApp> App, string DatabaseId) InitMessagingHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        AppConfiguration.ConfigurationStore configurationStore,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        var messagingHubDb = new MessagingHubDb(resourceGroup, cosmosdbAccount, myConfig).InitMessagingHubDb();
        new MessagingHubWorker(
            resourceGroup,
            messagingHubDb,
            configurationStore,
            managedEnvAndAppsTuples,
            dbOutput,
            gcpConfig).InitWorker();
        var messagingHubApps = new MessagingHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            messagingHubDb,
            myConfig,
            gcpConfig).InitMessagingHub();
        return (messagingHubApps, messagingHubDb.DatabaseId);
    }

    private static (List<App.ContainerApp> App, string DatabaseId) InitEmailHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        AppConfiguration.ConfigurationStore configurationStore,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        var emailHubDb = new EmailHubDb(resourceGroup, cosmosdbAccount, myConfig).InitEmailHubDb();
        new EmailHubWorker(
            resourceGroup,
            emailHubDb,
            configurationStore,
            managedEnvAndAppsTuples,
            dbOutput,
            gcpConfig).InitWorker();
        var emailHubApps = new EmailHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            emailHubDb,
            myConfig,
            gcpConfig).InitEmailHub();
        return (emailHubApps, emailHubDb.DatabaseId);
    }

    private static (
        List<App.ContainerApp> FlowHubApps,
        List<App.ContainerApp> FlowHubIntegratorApps,
        string DatabaseId) InitFlowHub(
            ResourceGroup resourceGroup,
            DocumentDB.DatabaseAccount cosmosdbAccount,
            AppConfiguration.ConfigurationStore configurationStore,
            ContainerRegistry.Registry registry,
            Output<string> adminUsername,
            Output<string> adminPassword,
            List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
            Db.DbOutput dbOutput,
            MyConfig myConfig,
            GcpConfig gcpConfig)
    {
        var flowHubDb = new FlowHubDb(
            resourceGroup,
            cosmosdbAccount,
            myConfig).InitFlowHubDb();
        var flowHubWorker = new FlowHubWorker(
            resourceGroup,
            flowHubDb,
            configurationStore,
            managedEnvAndAppsTuples,
            dbOutput,
            gcpConfig).InitWorker();
        var flowHubIntegrationDb = new FlowHubIntegrationDb(
            resourceGroup,
            cosmosdbAccount,
            myConfig).InitFlowHubIntegrationDb();
        var flowHubIntegratorApps = new FlowHubIntegrator(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            flowHubIntegrationDb,
            myConfig,
            gcpConfig).InitFlowHubIntegrator();
        var (flowHubApps, flowHubSharedResources) = new FlowHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            flowHubDb,
            myConfig,
            gcpConfig).InitFlowHub();
        var flowHubOnWorkflowExecutionEndedPostProcessRequestedEventConsumer =
            new FlowHubOnWorkflowExecutionEndedPostProcessRequestedEventConsumer(
                resourceGroup,
                flowHubDb,
                configurationStore,
                managedEnvAndAppsTuples,
                dbOutput,
                myConfig,
                gcpConfig,
                flowHubSharedResources).InitOnWorkflowExecutionEndedPostProcessRequestedEventConsumer();
        return (flowHubApps, flowHubIntegratorApps, flowHubDb.DatabaseId);
    }

    private static (List<App.ContainerApp> App, string DatabaseId) InitAuditHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        var auditHubDb = new AuditHubDb(resourceGroup, cosmosdbAccount, myConfig).InitAuditHubDb();
        var auditHubApps = new AuditHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            auditHubDb,
            myConfig,
            gcpConfig).InitAuditHub();
        return (auditHubApps, auditHubDb.DatabaseId);
    }

    private static (List<App.ContainerApp> App, string DatabaseId) InitPublicApiGateway(
        MyConfig myConfig,
        Db.DbOutput dbOutput,
        ResourceGroup resourceGroup,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        ContainerRegistry.Registry registry,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        GcpConfig gcpConfig)
    {
        var publicApiGatewayDb =
            new PublicApiGatewayDb(myConfig, resourceGroup, cosmosdbAccount).InitPublicApiGatewayDb();
        var publicApiGatewayApps = new PublicApiGateway(
            myConfig,
            dbOutput,
            resourceGroup,
            adminUsername,
            adminPassword,
            registry,
            managedEnvAndAppsTuples,
            publicApiGatewayDb,
            gcpConfig).InitPublicApiGateway();
        return (publicApiGatewayApps, publicApiGatewayDb.DatabaseId);
    }

    private static (List<App.ContainerApp> App, string DatabaseId) InitTenantHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig,
        (Dictionary<string, App.ContainerApp?>? Apps, Storage.StorageAccount? StorageAccount)? rbacApps)

    {
        var tenantHubDb = new TenantHubDb(resourceGroup, cosmosdbAccount, myConfig).InitTenantHubDb();
        var tenantHubApps = new Components.TenantHub.TenantHub(
                registry,
                adminUsername,
                adminPassword,
                resourceGroup,
                managedEnvAndAppsTuples,
                dbOutput,
                tenantHubDb,
                myConfig,
                gcpConfig,
                rbacApps ?? (new Dictionary<string, App.ContainerApp?>(), null))
            .InitTenantHub();
        return (tenantHubApps, tenantHubDb.DatabaseId);
    }

    private static (List<App.ContainerApp> App, string DatabaseId) InitIntelligentHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        AppConfiguration.ConfigurationStore configurationStore,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig,
        DocumentDB.DatabaseAccount vectorCosmosdbAccount)
    {
        ResourceGroup? kbAppServiceResourceGroup = null;
        var intelligentHubDb = new IntelligentHubDb(resourceGroup, cosmosdbAccount, myConfig).InitIntelligentHubDb();
        var intelligentHubVectorDb =
            new IntelligentHubVectorDb(resourceGroup, vectorCosmosdbAccount).InitIntelligentHubVectorDb();
        var (lightRagImage, lightRagImageName) = ImageUtils.CreateAndGetImageWithImageName(
            registry,
            adminUsername,
            adminPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.IntelligentHubLightRag),
            myConfig.BuildTime);

        if (myConfig.Name is "staging" or "production")
        {
            // Create a new resource group for Light RAG APP service which requires for the P4mv3 Tier
            kbAppServiceResourceGroup = new ResourceGroup("sleekflow-kb-resource-group-" + myConfig.Name);
        }

        new IntelligentHubLightRag(
            lightRagImageName,
            lightRagImage,
            myConfig,
            resourceGroup,
            kbAppServiceResourceGroup,
            adminUsername,
            adminPassword,
            registry,
            managedEnvAndAppsTuples).Initialize();
        new IntelligentHubLightRagWithNeo4J(
            lightRagImageName,
            lightRagImage,
            myConfig,
            resourceGroup,
            adminUsername,
            adminPassword,
            registry,
            managedEnvAndAppsTuples).Initialize();
        var intelligentHubApps = new Components.IntelligentHub.IntelligentHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            intelligentHubDb,
            myConfig,
            gcpConfig,
            intelligentHubVectorDb,
            configurationStore).InitIntelligentHub();
        return (intelligentHubApps, intelligentHubDb.DatabaseId);
    }

    private static List<App.ContainerApp> InitJourneyBuilderCustomActivity(
        ResourceGroup resourceGroup,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        MyConfig myConfig)
    {
        var journeyBuilderCustomActivityApps =
            new Components.SfmcJourneyBuilderCustomActivity.SfmcJourneyBuilderCustomActivity(
                registry,
                adminUsername,
                adminPassword,
                resourceGroup,
                managedEnvAndAppsTuples,
                myConfig).InitJourneyBuilderCustomActivity();

        return journeyBuilderCustomActivityApps;
    }

    public static (List<App.ContainerApp> App, string DatabaseId) InitUserEventHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        SignalRService.SignalR signalR,
        GcpConfig gcpConfig)
    {
        var userEventHubDb = new UserEventHubDb(resourceGroup, cosmosdbAccount, myConfig).InitUserEventHubDb();
        var userEventHubApps = new UserEventHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            userEventHubDb,
            myConfig,
            signalR,
            gcpConfig).InitUserEventHub();
        return (userEventHubApps, userEventHubDb.DatabaseId);
    }

    public static (List<App.ContainerApp> App, string DatabaseId) InitSupportHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        var supportHubDb = new SupportHubDb(resourceGroup, cosmosdbAccount).InitSupportHubDb();
        var supportHubApps = new SupportHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            supportHubDb,
            myConfig,
            gcpConfig).InitSupportHub();
        return (supportHubApps, supportHubDb.DatabaseId);
    }

    public static (List<App.ContainerApp> App, string DatabaseId) InitTicketingHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        var ticketingHubDb = new TicketingHubDb(resourceGroup, cosmosdbAccount).InitTicketingHubDb();
        var ticketingHubApps = new TicketingHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            ticketingHubDb,
            myConfig,
            gcpConfig).InitTicketingHub();
        return (ticketingHubApps, ticketingHubDb.DatabaseId);
    }

    public static (List<App.ContainerApp> App, string DatabaseId) InitInternalIntegrationHub(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount cosmosdbAccount,
        AppConfiguration.ConfigurationStore configurationStore,
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig,
        CoreSqlDbConfig coreSqlDbConfig)
    {
        var internalIntegrationHubDb =
            new InternalIntegrationHubDb(resourceGroup, cosmosdbAccount, myConfig).InitInternalIntegrationHubDb();
        new InternalIntegrationHubWorker(
            resourceGroup,
            internalIntegrationHubDb,
            configurationStore,
            managedEnvAndAppsTuples,
            dbOutput,
            gcpConfig).InitWorker();
        var internalIntegrationHubApps = new InternalIntegrationHub(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            dbOutput,
            internalIntegrationHubDb,
            myConfig,
            gcpConfig,
            coreSqlDbConfig).InitInternalIntegrationHub();
        return (internalIntegrationHubApps, internalIntegrationHubDb.DatabaseId);
    }

    public static (Dictionary<string, App.ContainerApp?>? Apps, StorageAccount? StorageAccount) InitOpa(
        ContainerRegistry.Registry registry,
        Output<string> adminUsername,
        Output<string> adminPassword,
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        var opa = new Opa(
            registry,
            adminUsername,
            adminPassword,
            resourceGroup,
            managedEnvAndAppsTuples,
            myConfig,
            gcpConfig).InitOpa();
        return (opa.Apps, opa.StorageAccount);
    }

    [Output]
    public Output<string> FrontDoorEndpoint { get; set; }

    [Output]
    public Output<string> InternalGatewayApplicationUrl { get; set; }

    private static (ContainerRegistry.Registry Registry, Output<string> AdminUsername, Output<string> AdminPassword)
        InitContainerRegistry(ResourceGroup resourceGroup)
    {
        var registry = new ContainerRegistry.Registry(
            "myregistry",
            new ContainerRegistry.RegistryArgs
            {
                ResourceGroupName = resourceGroup.Name,
                Sku = new ContainerRegistry.Inputs.SkuArgs
                {
                    Name = ContainerRegistry.SkuName.Basic
                },
                AdminUserEnabled = true
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var registryCredentials = ContainerRegistry.ListRegistryCredentials.Invoke(
            new ContainerRegistry.ListRegistryCredentialsInvokeArgs
            {
                ResourceGroupName = resourceGroup.Name, RegistryName = registry.Name
            });
        var adminUsername = registryCredentials.Apply(c => c.Username ?? string.Empty);
        var adminPassword =
            registryCredentials.Apply(c => Output.CreateSecret(c.Passwords.First().Value ?? string.Empty));
        return (registry, adminUsername, adminPassword);
    }
}