﻿using System.Net.Mime;
using System.Text;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.Extensions;
using Sleekflow.JsonConfigs;

namespace Sleekflow.WebhookBridge.StepExecutorRequestBuilders;

public interface IStepExecutorBuilder : IScopedService
{
    Task<HttpRequestMessage> BuildAsync(
        string requestUrl,
        Dictionary<string, object?>? headers,
        string? bodyStr,
        Dictionary<string, object?>? bodyDict,
        HttpMethod httpMethod);
}

public class StepExecutorBuilder : IStepExecutorBuilder
{
    public Task<HttpRequestMessage> BuildAsync(
        string requestUrl,
        Dictionary<string, object?>? headers,
        string? bodyStr,
        Dictionary<string, object?>? bodyDict,
        HttpMethod httpMethod)
    {
        var request = new HttpRequestMessage(
            httpMethod,
            requestUrl);

        var contentType = GetRequestContentType(headers);

        foreach (var (key, value) in headers!)
        {
            request.Headers.Add(key, value as string ?? string.Empty);
        }

        request.Content = GetRequestContent(
            contentType,
            bodyStr,
            bodyDict);

        return Task.FromResult(request);
    }


    private static string GetRequestContentType(IDictionary<string, object?>? headersDict)
    {
        if (headersDict is null
            || !headersDict.TryGetValue(HeaderNames.ContentType, out var contentTypeObj))
        {
            throw new InvalidOperationException($"Missing required header {HeaderNames.ContentType}");
        }

        if (contentTypeObj is not string)
        {
            var headerValue = contentTypeObj is null ? string.Empty : JsonConvert.SerializeObject(contentTypeObj);
            throw new InvalidOperationException($"{HeaderNames.ContentType} header invalid value '{headerValue}'");
        }

        headersDict.Remove(HeaderNames.ContentType);

        return Convert.ToString(contentTypeObj)!.ToLower();
    }

    private static HttpContent GetRequestContent(
        string contentType,
        string? bodyStr,
        Dictionary<string, object?>? bodyDict)
    {
        return contentType switch
        {
            MediaTypeNames.Text.Plain => ConstructStringContent(contentType, bodyStr, null),
            MediaTypeNames.Text.Html => ConstructStringContent(contentType, bodyStr, null),
            MediaTypeNames.Text.Xml => ConstructStringContent(contentType, bodyStr, null),
            MediaTypeNames.Application.Xml => ConstructStringContent(contentType, bodyStr, null),
            MediaTypeNames.Application.Json => ConstructStringContent(contentType, bodyStr, bodyDict),
            "application/x-www-form-urlencoded" => ConstructFormUrlEncodedContent(bodyStr, bodyDict),
            "multipart/form-data" => ConstructFormDataContent(bodyStr, bodyDict),
            _ => throw new NotImplementedException($"Content type {contentType} is not supported for HTTP POST step")
        };
    }

    private static HttpContent ConstructStringContent(
        string contentType,
        string? bodyStr,
        Dictionary<string, object?>? bodyDict)
    {
        return new StringContent(
            bodyStr ?? JsonConvert.SerializeObject(
                bodyDict ?? new object(),
                JsonConfig.DefaultJsonSerializerSettings),
            Encoding.UTF8,
            contentType);
    }


    private static HttpContent ConstructFormUrlEncodedContent(string? bodyStr, Dictionary<string, object?>? bodyDict)
    {
        return new FormUrlEncodedContent(
            AggregateContentDict(
                bodyStr,
                bodyDict));
    }

    private static HttpContent ConstructFormDataContent(string? bodyStr, Dictionary<string, object?>? bodyDict)
    {
        var contentDict = AggregateContentDict(bodyStr, bodyDict);

        var formDataContent = new MultipartFormDataContent();

        foreach (var keyValuePair in contentDict)
        {
            formDataContent.Add(
                new StringContent(keyValuePair.Value, Encoding.UTF8),
                keyValuePair.Key);
        }

        return formDataContent;
    }

    private static Dictionary<string, string> AggregateContentDict(
        string? bodyStr,
        Dictionary<string, object?>? bodyDict)
    {
        var contentDict = new Dictionary<string, string>();

        if (bodyStr is not null
            && bodyStr.TryParseToJToken(out var token)
            && token!.Type is JTokenType.Object)
        {
            var bodyStrDict = JsonConvert.DeserializeObject<Dictionary<string, object?>>(bodyStr);

            foreach (var keyValuePair in bodyStrDict!)
            {
                contentDict.TryAdd(
                    keyValuePair.Key,
                    keyValuePair.Value as string ?? JsonConvert.SerializeObject(
                        keyValuePair.Value ?? string.Empty,
                        JsonConfig.DefaultJsonSerializerSettings));
            }
        }

        if (bodyDict is not null)
        {
            foreach (var keyValuePair in bodyDict)
            {
                contentDict.TryAdd(
                    keyValuePair.Key,
                    keyValuePair.Value as string ?? JsonConvert.SerializeObject(
                        keyValuePair.Value ?? string.Empty,
                        JsonConfig.DefaultJsonSerializerSettings));
            }
        }

        return contentDict;
    }
}