﻿using Sleekflow.Exceptions;
using Sleekflow.Infras.Constants;

namespace Sleekflow.Infras.Components.VNet;

public static class VnetConfig
{
    // Constants for environment names
    private const string Dev = "dev";
    private const string Staging = "staging";
    private const string Production = "production";

    public static string GetAddressPrefix(string envConfigName, string locationName)
    {
        // Validate input parameters
        if (string.IsNullOrEmpty(envConfigName))
        {
            throw new SfInternalErrorException("Environment name cannot be null or empty");
        }

        if (string.IsNullOrEmpty(locationName))
        {
            throw new SfInternalErrorException("Location name cannot be null or empty");
        }

        // Validate environment name
        var addressPrefixDict = envConfigName.ToLowerInvariant() switch
        {
            Dev => DevAddressPrefix,
            Staging => StagingAddressPrefix,
            Production => ProductionAddressPrefix,
            _ => throw new SfInternalErrorException($"Invalid environment: '{envConfigName}'. Expected: {Dev}, {Staging}, or {Production}")
        };

        // Validate location name
        if (!addressPrefixDict.TryGetValue(locationName, out var addressPrefix))
        {
            throw new SfInternalErrorException($"Address prefix not configured for location: '{locationName}' in environment: '{envConfigName}'");
        }

        return addressPrefix;
    }

    public static string GetSubnetAddressPrefix(string envConfigName, string locationName)
    {
        // Validate input parameters
        if (string.IsNullOrEmpty(envConfigName))
        {
            throw new SfInternalErrorException("Environment name cannot be null or empty");
        }

        if (string.IsNullOrEmpty(locationName))
        {
            throw new SfInternalErrorException("Location name cannot be null or empty");
        }

        // Validate environment name
        var subAddressPrefixDict = envConfigName.ToLowerInvariant() switch
        {
            Dev => DevSubAddressPrefix,
            Staging => StagingSubAddressPrefix,
            Production => ProductionSubAddressPrefix,
            _ => throw new SfInternalErrorException($"Invalid environment: '{envConfigName}'. Expected: {Dev}, {Staging}, or {Production}")
        };

        // Validate location name
        if (!subAddressPrefixDict.TryGetValue(locationName, out var subAddressPrefix))
        {
            throw new SfInternalErrorException($"Subnet address prefix not configured for location: '{locationName}' in environment: '{envConfigName}'");
        }

        return subAddressPrefix;
    }


    private static readonly Dictionary<string, string> DevAddressPrefix = new()
    {
        { LocationNames.EastAsia, "**********/16" },
    };

    private static readonly Dictionary<string, string> StagingAddressPrefix = new()
    {
        { LocationNames.EastAsia, "**********/16" },
    };

    private static readonly Dictionary<string, string> ProductionAddressPrefix = new()
    {
        { LocationNames.EastAsia, "**********/16" },
    };

    private static readonly Dictionary<string, string> DevSubAddressPrefix = new()
    {
        { LocationNames.EastAsia, "**********/23" },
    };

    private static readonly Dictionary<string, string> StagingSubAddressPrefix = new()
    {
        { LocationNames.EastAsia, "**********/23" },
    };

    private static readonly Dictionary<string, string> ProductionSubAddressPrefix = new()
    {
        { LocationNames.EastAsia, "**********/23" },
    };
}