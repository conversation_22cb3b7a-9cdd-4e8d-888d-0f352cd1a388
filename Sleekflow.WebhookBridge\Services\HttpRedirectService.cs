﻿using Sleekflow.DependencyInjection;

namespace Sleekflow.WebhookBridge.Services;

public interface IHttpRedirectService
{
    Task Redirect(HttpRequest request);

    Task<(bool IsSuccess, string Response)> Redirect(HttpRequestMessage request);
}

public class HttpRedirectService : ISingletonService, IHttpRedirectService
{
    private const string TargetPath = "X-Target-Url";
    private readonly HttpClient _httpClient;
    private readonly ILogger<HttpRedirectService> _logger;

    public HttpRedirectService(HttpClient httpClient, ILogger<HttpRedirectService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task Redirect(HttpRequest request)
    {
        try
        {
            if (!request.Headers.TryGetValue(TargetPath, out var pathValues))
            {
                throw new Exception("Target path header not found");
            }

            var targetUri = new Uri(pathValues);

            var requestMessage = new HttpRequestMessage
            {
                Method = new HttpMethod(request.Method),
                RequestUri = targetUri,
                Content = new StreamContent(request.Body)
            };

            // Copy headers from the original request to the new request
            foreach (var header in request.Headers)
            {
                // Skip the Host and our own custom path header
                if (!header.Key.Equals("Host", StringComparison.OrdinalIgnoreCase) &&
                    !header.Key.Equals(TargetPath, StringComparison.OrdinalIgnoreCase))
                {
                    requestMessage.Content.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
                    requestMessage.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
                }
            }

            var responseMessage = await _httpClient.SendAsync(requestMessage);
            var resStr = await responseMessage.Content.ReadAsStringAsync();

            _logger.LogInformation(
                "[Webhook Bridge] Redirect HTTP request. Target Url: {Target}, StatusCode: {StatusCode}, ResponseBody: {ResponseBody}",
                pathValues,
                responseMessage.StatusCode,
                resStr);

            if (!responseMessage.IsSuccessStatusCode)
            {
                throw new Exception("Failed to send HTTP response");
            }
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[Webhook Bridge] Errors when redirecting Http Request : {Message}",
                e.Message);
        }
    }

    public async Task<(bool IsSuccess, string Response)> Redirect(HttpRequestMessage request)
    {
        try
        {
            var responseMessage = await _httpClient.SendAsync(request);
            var resStr = await responseMessage.Content.ReadAsStringAsync();

            if (!responseMessage.IsSuccessStatusCode)
            {
                throw new Exception(resStr);
            }

            _logger.LogInformation(
                "[Webhook Bridge] Redirect HTTP HttpRequestMessage. Target Url: {Target}, StatusCode: {StatusCode}, ResponseBody: {ResponseBody}",
                request.RequestUri,
                responseMessage.StatusCode,
                resStr);

            return (true, resStr);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[Webhook Bridge] Errors when redirecting Http Request : {Message}",
                e.Message);
            return (false, e.Message);
        }
    }
}