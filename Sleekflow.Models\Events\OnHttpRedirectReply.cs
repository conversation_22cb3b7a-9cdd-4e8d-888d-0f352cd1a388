﻿using Newtonsoft.Json;

namespace Sleekflow.Models.Events;

public class OnHttpRedirectReply
{
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("http_response_message")]
    public string HttpResponseMessage { get; set; }

    [JsonConstructor]
    public OnHttpRedirectReply(bool success, string httpResponseMessage)
    {
        Success = success;
        HttpResponseMessage = httpResponseMessage;
    }
}