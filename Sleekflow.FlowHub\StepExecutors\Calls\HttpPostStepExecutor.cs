using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Https.Builders;
using Sleekflow.FlowHub.Https.Services;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IHttpPostStepExecutor : IStepExecutor
{
}

public class HttpPostStepExecutor
    : GeneralStepExecutor<CallStep<HttpPostStepArgs>>, IHttpPostStepExecutor, IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly HttpClient _httpClient;
    private readonly IWebhookBridgeRedirectService _webhookBridgeRedirectService;
    private readonly IStateAggregator _stateAggregator;
    private readonly IHttpRequestBuilder _requestBuilder;
    private readonly ILogger<HttpPostStepExecutor> _logger;

    public HttpPostStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IHttpClientFactory httpClientFactory,
        IWebhookBridgeRedirectService webhookBridgeRedirectService,
        IStateAggregator stateAggregator,
        IHttpRequestBuilder requestBuilder,
        ILogger<HttpPostStepExecutor> logger)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _httpClient = httpClientFactory.CreateClient("default-flow-hub-handler");
        _webhookBridgeRedirectService = webhookBridgeRedirectService;
        _stateAggregator = stateAggregator;
        _requestBuilder = requestBuilder;
        _logger = logger;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var urlStr =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.UrlExpr)
                          ?? callStep.Args.UrlExpr);
            var headersDict =
                callStep.Args.HeadersKeyExprDict == null
                    ? new Dictionary<string, object?>()
                    : await _stateEvaluator.EvaluateDictExpressionAsync(state, callStep.Args.HeadersKeyExprDict);
            var bodyStr =
                callStep.Args.BodyExpr != null
                    ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.BodyExpr)
                                ?? callStep.Args.BodyExpr)
                    : null;
            var bodyDict =
                callStep.Args.BodyKeyExprDict != null
                    ? await _stateEvaluator.EvaluateDictExpressionAsync(state, callStep.Args.BodyKeyExprDict)
                    : null;

            var reqMsg = await _requestBuilder.BuildAsync(urlStr, headersDict, bodyStr, bodyDict, HttpMethod.Post);

            var requestBodyForLogging = string.Empty;
            if (reqMsg.Content != null)
            {
                requestBodyForLogging = await reqMsg.Content.ReadAsStringAsync();
            }

            _logger.LogInformation(
                "[{StepExecutor}] Sending HTTP request: WorkflowId: {WorkflowId}, StateId: {StateId}, StepId: {StepId}, Url: {Url}, Headers: {Headers}, RequestBody: {RequestBody}",
                GetType().Name,
                workflow.Id,
                state.Id,
                step.Id,
                urlStr,
                reqMsg.Headers?.ToString(),
                requestBodyForLogging);

            var resMsg = await _httpClient.SendAsync(reqMsg);
            var resStr = await resMsg.Content.ReadAsStringAsync();

            _logger.LogInformation(
                "[{StepExecutor}] Received HTTP response. WorkflowId: {WorkflowId}, StateId: {StateId}, StepId: {StepId}, StatusCode: {StatusCode}, ResponseBody: {ResponseBody}",
                GetType().Name,
                workflow.Id,
                state.Id,
                step.Id,
                resMsg.StatusCode,
                resStr);

            if (!resMsg.IsSuccessStatusCode)
            {
                throw new SfFlowHubUserFriendlyException(
                    UserFriendlyErrorCodes.InternalError,
                    $"HTTP request failed with status {(int) resMsg.StatusCode}");
            }

            var updatedState = await _stateAggregator.AggregateStateStepBodyAsync(
                state,
                step.Id,
                resStr);

            await onActivatedAsync(updatedState, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }
}