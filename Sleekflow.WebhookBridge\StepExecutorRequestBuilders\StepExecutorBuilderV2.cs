﻿namespace Sleekflow.WebhookBridge.RequestBuilders;

public class StepExecutorBuilderV2
{


    // public async Task<> Build()
    // {
    //     var reqMsg = new HttpRequestMessage(httpMethod, urlStr);
    //
    //     foreach (var (key, value) in headersDict)
    //     {
    //         reqMsg.Headers.Add(key, value as string ?? string.Empty);
    //     }
    //
    //     if (httpMethod != HttpMethod.Get && httpMethod != HttpMethod.Delete && !string.IsNullOrEmpty(callStep.Args.BodyExpr))
    //     {
    //         var bodyContent = (string) (await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, callStep.Args.BodyExpr)
    //                                     ?? callStep.Args.BodyExpr);
    //
    //         var contentType = callStep.Args.ContentType ?? "application/json";
    //         var bodyType = callStep.Args.BodyType ?? "text";
    //
    //         reqMsg.Content = new StringContent(bodyContent, Encoding.UTF8, contentType);
    //         if (!string.IsNullOrEmpty(bodyType))
    //         {
    //             reqMsg.Content.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("type", bodyType));
    //         }
    //     }
    //
    //     var requestBodyForLogging = string.Empty;
    //     if (reqMsg.Content != null)
    //     {
    //         requestBodyForLogging = await reqMsg.Content.ReadAsStringAsync();
    //     }
    //
    // }
}