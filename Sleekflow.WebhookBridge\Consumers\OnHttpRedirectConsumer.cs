﻿using MassTransit;
using Sleekflow.Models.Events;
using Sleekflow.WebhookBridge.RequestBuilders;
using Sleekflow.WebhookBridge.Services;
using Sleekflow.WebhookBridge.StepExecutorRequestBuilders;

namespace Sleekflow.WebhookBridge.Consumers;

public class OnHttpRedirectConsumerDefinition
    : ConsumerDefinition<OnHttpRedirectConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnHttpRedirectConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(1);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Interval(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnHttpRedirectConsumer : IConsumer<OnHttpRedirectRequest>
{
    private readonly IHttpRedirectService _httpRedirectService;
    private readonly IStepExecutorBuilder _stepExecutorBuilder;

    public OnHttpRedirectConsumer(IHttpRedirectService httpRedirectService, IStepExecutorBuilder stepExecutorBuilder)
    {
        _httpRedirectService = httpRedirectService;
        _stepExecutorBuilder = stepExecutorBuilder;
    }

    public async Task Consume(ConsumeContext<OnHttpRedirectRequest> context)
    {
        var req = context.Message;

        var url = req.Url;
        var method = req.Method;

        var headerDict = req.HeaderDict;

        var bodyStr = req.BodyStr;
        var bodyDict = req.BodyDict;


        var httpMessage = await _stepExecutorBuilder.BuildAsync(url, headerDict, bodyStr, bodyDict, method);

        var (isSuccess, response) = await _httpRedirectService.Redirect(httpMessage);

        var onHttpRedirectEventResult = new OnHttpRedirectReply(isSuccess, response);

        await context.RespondAsync<OnHttpRedirectReply>(onHttpRedirectEventResult);
    }
}