using Pulumi;
using Pulumi.AzureNative.Network.Inputs;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Network = Pulumi.AzureNative.Network;

namespace Sleekflow.Infras.Components.VNet;

public class WebhookBridgeVNet
{
    private readonly ResourceGroup _resourceGroup;
    private readonly MyConfig _myConfig;
    private readonly string _locationName;

    public WebhookBridgeVNet(ResourceGroup resourceGroup, string locationName, MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _myConfig = myConfig;
        _locationName = locationName;
    }

    public WebhookBridgeVNetOutput InitWebhookBridgeVNet()
    {
        var location = LocationNames.GetAzureLocation(_locationName);
        var locationShortName = LocationNames.GetShortName(_locationName);

        // Create Public IP for NAT Gateway
        var publicIpName = $"sleekflow-webhookbridge-ip-{locationShortName}";
        var publicIp = new Network.PublicIPAddress(
            publicIpName,
            new Network.PublicIPAddressArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location = location,
                PublicIpAddressName = publicIpName,
                Sku = new PublicIPAddressSkuArgs
                {
                    Name = Network.PublicIPAddressSkuName.Standard, Tier = Network.PublicIPAddressSkuTier.Regional
                },
                PublicIPAllocationMethod = Network.IPAllocationMethod.Static,
                PublicIPAddressVersion = Network.IPVersion.IPv4,
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Create NAT Gateway
        var natGatewayName = $"sleekflow-webhookbridge-nat-{locationShortName}";
        var natGateway = new Network.NatGateway(
            natGatewayName,
            new Network.NatGatewayArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location = location,
                NatGatewayName = natGatewayName,
                Sku = new Network.Inputs.NatGatewaySkuArgs
                {
                    Name = Network.NatGatewaySkuName.Standard
                },
                PublicIpAddresses = new InputList<Network.Inputs.SubResourceArgs>
                {
                    new Network.Inputs.SubResourceArgs
                    {
                        Id = publicIp.Id
                    }
                },
                IdleTimeoutInMinutes = 4,
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Create VNet for WebhookBridge
        var vnetName = $"sleekflow-webhookbridge-vnet-{locationShortName}";
        var vnet = new Network.VirtualNetwork(
            vnetName,
            new Network.VirtualNetworkArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location = location,
                VirtualNetworkName = vnetName,
                AddressSpace = new Network.Inputs.AddressSpaceArgs
                {
                    AddressPrefixes = new InputList<string>
                    {
                        VnetConfig.GetAddressPrefix(_myConfig.Name, location)
                    }
                },
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Create subnet for Container Apps Environment and associate it with the NAT Gateway
        var subnetName = $"sleekflow-webhookbridge-subnet-{locationShortName}";
        var subnet = new Network.Subnet(
            subnetName,
            new Network.SubnetArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                VirtualNetworkName = vnet.Name,
                SubnetName = subnetName,
                AddressPrefix = VnetConfig.GetSubnetAddressPrefix(_myConfig.Name, location),
                NatGateway = new Network.Inputs.SubResourceArgs
                {
                    Id = natGateway.Id
                },
                Delegations = new InputList<DelegationArgs>()
                {
                    new DelegationArgs
                    {
                        Name = "Microsoft.App/environments",
                        Type = "Microsoft.Network/virtualNetworks/subnets/delegations",
                        ServiceName = "Microsoft.App/environments"
                    }
                },
                PrivateEndpointNetworkPolicies = Network.VirtualNetworkPrivateEndpointNetworkPolicies.Disabled,
                PrivateLinkServiceNetworkPolicies = Network.VirtualNetworkPrivateLinkServiceNetworkPolicies.Enabled
            },
            new CustomResourceOptions
            {
                Parent = vnet,
                DependsOn = new InputList<Pulumi.Resource>
                {
                    vnet
                }
            });

        return new WebhookBridgeVNetOutput(vnet, subnet, publicIp, natGateway, publicIp.IpAddress!);
    }
}